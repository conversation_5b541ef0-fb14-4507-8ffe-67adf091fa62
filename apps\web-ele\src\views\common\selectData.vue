<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { Search } from '@vben/icons';

import { ElButton, ElIcon, ElInput, ElMessage } from 'element-plus';

import { requestClient } from '#/api/request';
import ClientGridComponent from '#/components/ag-grid/ClientGridComponent.vue';

interface ColumnDef {
  title?: string;
  headerName?: string;
  dataIndex?: string;
  field?: string;
  [key: string]: any;
  class?: string;
}

interface Props {
  // 数据源配置
  data?: any[];
  api?: string;
  params?: Record<string, any>;
  // 列配置
  columns: ColumnDef[];
  // 选择配置
  multiple?: boolean;
  selectedKeys?: any[];
  // UI配置
  title?: string;
  showSearch?: boolean;
  searchPlaceholder?: string;
  showPagination?: boolean;
  class?: string;
  // 其他配置
  immediate?: boolean;
  transformResponse?: (response: any) => any[];
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  columns: () => [],
  multiple: false,
  selectedKeys: () => [],
  api: '',
  params: () => ({}),
  title: '请选择',
  showSearch: true,
  searchPlaceholder: '请输入搜索内容',
  showPagination: false,
  immediate: true,
  transformResponse: (response: any) => response,
  class: 'w-[40%] h-[60%]',
});

// 添加一个计算属性用于过滤数据
const handleFilter = (val: string) => {
  gridRef.value.gridApi.setGridOption('quickFilterText', val);
};

// 状态管理
const loading = ref(false);
const searchValue = ref('');
const gridRef = ref();
const rowData = ref<any[]>([]);
const total = ref(0);

// AG-Grid 配置
const defaultColDef = {
  sortable: true,
  resizable: true,
  filter: true,
};

const columnDefs = computed(() => {
  return props.columns.map((col) => ({
    ...col,
    headerName: col.title || col.headerName,
    field: col.dataIndex || col.field,
  }));
});

const rowSelection = computed(() => ({
  mode: props.multiple
    ? ('multiRow' as 'multiRow')
    : ('singleRow' as 'singleRow'),
  checkboxes: true,
  headerCheckbox: props.multiple,
  enableClickSelection: true,
}));

// 加载数据
const loadData = async () => {
  if (!props.api && props.data.length > 0) {
    rowData.value = props.data;
    return;
  }

  if (!props.api) return;

  try {
    loading.value = true;
    const res = await requestClient.post<any>(props.api, props.params);
    if (res) {
      const transformedData = props.transformResponse(res);
      rowData.value = transformedData;
      total.value = transformedData.length;
    }
  } catch (error) {
    console.error('加载数据失败:', error);
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

// 事件处理
const handleSearch = () => {
  if (props.api) {
    loadData();
  }
};

// 获取选中数据
const getSelectedData = () => {
  if (!gridRef.value?.gridApi) return null;

  const selectedNodes = gridRef.value.gridApi.getSelectedNodes();
  if (!selectedNodes || selectedNodes.length === 0) {
    ElMessage.warning('请选择数据');
    return null;
  }

  return selectedNodes.map((node: any) => node.data);
};

// Modal 配置
const [Modal, modalApi] = useVbenModal({
  closeOnClickModal: false,
  draggable: true,
  zIndex: 2100, // 设置更高的 z-index，确保在 prompt 弹窗之上
  onOpenChange(isOpen) {
    if (isOpen) {
      loadData();
    }
  },
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    // 获取到当前选择的数据
    const selectedData = getSelectedData();
    if (!selectedData) return;
    modalApi.sharedData = selectedData;
    modalApi.close(); // 关闭时返回选中的数据
  },
});

// 组件挂载
onMounted(() => {
  if (props.immediate) {
    loadData();
  }
});

// 暴露方法和属性给父组件
defineExpose({
  modalApi,
  getSelectedData,
  loadData,
  gridRef,
});
</script>

<template>
  <Modal :class="props.class" :title="title" class="min-w-[1000px]">
    <div style="height: 100%">
      <!-- 搜索区域 -->
      <div class="search-box" v-if="showSearch">
        <ElInput
          v-model="searchValue"
          :placeholder="searchPlaceholder"
          style="flex: 1"
          class="input-with-select"
          @input="handleFilter"
          @keyup.enter="handleSearch"
        >
          <template #append>
            <ElButton :loading="loading" @click="handleSearch">
              <ElIcon>
                <Search />
              </ElIcon>
            </ElButton>
          </template>
        </ElInput>
      </div>
      <div style="height: 90%">
        <!-- 表格区域 -->
        <ClientGridComponent
          ref="gridRef"
          :row-data="rowData"
          :column-defs="columnDefs"
          :row-selection="rowSelection"
          :default-col-def="defaultColDef"
          :page-size="20"
        />
      </div>
    </div>
  </Modal>
</template>

<style scoped>
.search-box {
  margin-bottom: 16px;
}
</style>
